@charset "utf-8";
/* CSS Document */

body {
    background-color: #28397A;
    color: #333333;
    background-image: url(img/tftbackground.jpg);
	background-size:cover;
	background-repeat: no-repeat;
	background-pos: center;
		
}
a:link {
    color: #3366FF;
}
a:visited {
    color: #9999FF;
}
a:hover {
    color: #FF6666;
}
a:active {
    color: #CC0000;
    font-size: 24px;
}
h1 {
    font-size: xx-large;
}
p {
    font-size: small;
    font-family: "Hiragino Sans", "Hiragino Kaku Gothic ProN", Meiryo, sans-serif;
    line-height: 140%;
}
li {
    font-size: small;
    color: #000000;
}
hr {
    display: none;
}
#container {
    width: 750px;
    background-color: #fff;
    margin-top: 30px;
    margin-right: auto;
    margin-left: auto;
    border: thin solid #66B4CC;
}
#header {
    width: 750px;
}
#gnavbar {
    width: 750px;
}
#contents {
    width: 750px;
    color: #504D6A;
}
#footer {
    width: 740px;
    background-color: #6FA7DC;
    padding: 5px;
    font-family: "Hiragino Sans", "Hiragino Kaku Gothic ProN", <PERSON><PERSON>, sans-serif;
    font-weight: bold;
    color: #fff;
}
#sidemenu {
    float: left;
    width: 150px;
}
#main {
    float: right;
    width: 560px;
    padding-top: 25px;
    padding-right: 15px;
    padding-bottom: 25px;
    padding-left: 25px;
    color: #388CB3;
}
.clearfloat {
    font-family: "Hiragino Sans", "Hiragino Kaku Gothic ProN", Meiryo, sans-serif;
    font-size: 1px;
    line-height: 0px;
    clear: both;
    height: 0px;
    color: #B1718C;
}
body, h1, h2, h3, p {
    margin: 0px;
    padding: 0px;
    color: #000000;
    text-align: left;
    font-size: 16px;
    font-family: "Hiragino Sans", "Hiragino Kaku Gothic ProN", Meiryo, sans-serif;
}
#main h2 {
    border-top: 1px solid #303892;
    border-right: 1px solid #303892;
    border-bottom: 1px solid #303892;
    border-left: 10px solid #303892;
    font-family: "Hiragino Sans", "Hiragino Kaku Gothic ProN", Meiryo, sans-serif;
    font-size: medium;
    font-style: normal;
    color: #B53DC7;
    letter-spacing: 0.2em;
    text-indent: 0.5em;
    padding: 4px;
    margin-top: 15px;
    margin-bottom: 10px;
    background-image: url(images/Chap7up/titlebk.jpg);
    background-repeat: repeat-y;
    background-position: right;
    text-align: left;
}
#main h3 {
    font-family: "Hiragino Sans", "Hiragino Kaku Gothic ProN", Meiryo, sans-serif;
    font-size: medium;
    font-weight: bold;
    color: #F86857;
    margin: 5px;
}
#sidemenu h2 {
    font-family: "Hiragino Sans", "Hiragino Kaku Gothic ProN", Meiryo, sans-serif;
    font-size: small;
    font-weight: bold;
    color: #fff;
    background-color: #977EFF;
    letter-spacing: 0.1em;
    text-indent: 0.5em;
    padding: 1px;
    width: 120px;
    margin-top: 15px;
    margin-right: 0px;
    margin-bottom: 0px;
    margin-left: 10px;
}
#gnavbar ul {
    margin: 0px;
    padding: 0px;
    list-style-type: none;
}
#gnavbar ul li {
    float: left;
    width: 150px;
    text-align: center;
}
#gnavbar a {
    background-color: #9c3;
    display: block;
    font-family: "Hiragino Sans", "Hiragino Kaku Gothic ProN", Meiryo, sans-serif;
    font-size: small;
    font-weight: bold;
    color: #fff;
    text-decoration: none;
    text-transform: uppercase;
    padding: 4px;
    background-image: url(images/btn_a.jpg);
}
#gnavbar a:hover {
    background-color: #693;
    background-image: url(images/btn_b.jpg);
    font-family: "Hiragino Sans", "Hiragino Kaku Gothic ProN", Meiryo, sans-serif;
    color: #6693ED;
}
#sidemenu ul {
    background-color: #977EFF;
    padding: 1px;
    margin-top: 0px;
    margin-right: 5px;
    margin-bottom: 0px;
    margin-left: 10px;
    list-style-type: none;
}
#sidemenu a {
    background-color: #fff;
    font-family: "Hiragino Sans", "Hiragino Kaku Gothic ProN", Meiryo, sans-serif;
    color: #333;
    text-decoration: none;
    display: block;
    padding: 3px;
    margin: 2px;
}
#sidemenu a:hover {
    background-color: #A79FEC;
    font-family: "Hiragino Sans", "Hiragino Kaku Gothic ProN", Meiryo, sans-serif;
    color: #fff;
}
.photoblock {
    float: left;
    width: 253px;
    padding: 10px;
    margin-top: 1px;
    margin-right: 1px;
    margin-bottom: 5px;
    margin-left: 3px;
    border-top: 1px solid #1400FF;
    border-right: 1px solid #1400FF;
    border-bottom: 1px solid #1400FF;
    border-left: 1px solid #1400FF;
	text-align: center;
}
.title1 {
    font-family: "Hiragino Sans", "Hiragino Kaku Gothic ProN", Meiryo, sans-serif;
    font-size: medium;
    font-weight: bold;
    color: #3747F8;
    text-align: center;
}
.slider {
    position: relative;
    width: 560px; 
    height: 300px; 
    overflow: hidden;
    margin: 20px auto; 
}

.slides {
    display: flex;
    width: 200%;
    height: 100%;
    transition: transform 0.5s ease;
}

.slide {
    width: 50%;
    height: 100%;
    flex-shrink: 0;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

input[type="radio"] {
    display: none;
}

input[type="radio"]:nth-child(1):checked ~ .slides {
    transform: translateX(0);
}

input[type="radio"]:nth-child(2):checked ~ .slides {
    transform: translateX(-50%);
}

.navigation {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    pointer-events: none;
}

.nav-button {
    width: 50px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.5);
    color: white;
    font-size: 2rem;
    text-align: center;
    pointer-events: auto;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.3s;
}

.nav-button:hover {
    background-color: rgba(0, 0, 0, 0.7);
}

.left-button {
    position: absolute;
    left: 0;
}

.right-button {
    position: absolute;
    right: 2px;
    top: -3px;
}

input[type="radio"]:nth-child(1):checked ~ .navigation .left-button {
    display: none;
}

input[type="radio"]:nth-child(2):checked ~ .navigation .right-button {
    display: none;
}
.centered-image {
    text-align: center; 
}

.centered-image img {
    display: block; 
    margin: 0 auto;  
}
.video-container {
    width: 80%;
    max-width: 560px;
    height: auto;
}

iframe {
    width: 100%;
    height: 315px; 
    border: none; 
}
.custom-text {
    font-size: 20px;
    color: #BAA515;
}
.button {
    display: inline-block;
    background-color: #FFD700; 
    color: #FFFFFF;
    padding: 15px 32px;
    text-align: center;
    text-decoration: none;
    font-size: 16px;
    margin: 4px 2px;
    cursor: pointer;
    border-radius: 12px;
    border: none;
}

.button:hover {
    background-color: #45a049;
    color: #2441D0;
}
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}
.centered-text {
    text-align: center;
    color: #FFFFFF;
}
